name: flutterclient
description: "Agent Assistant Flutter Client - Mobile client for AI agent communication"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.5.0

dependencies:
  flutter:
    sdk: flutter

  # UI and Material Design
  cupertino_icons: ^1.0.8
  material_color_utilities: ^0.11.1

  # State Management
  provider: ^6.1.2

  # Network and WebSocket
  web_socket_channel: ^3.0.1
  http: ^1.2.2

  # Protobuf
  protobuf: ^4.1.0
  fixnum: ^1.1.0

  # Storage and Persistence
  shared_preferences: ^2.3.2
  path_provider: ^2.1.4
  sqflite: ^2.3.3+1

  # Utilities
  uuid: ^4.5.1
  intl: ^0.19.0
  url_launcher: ^6.3.1

  # Media and Content
  cached_network_image: ^3.4.1
  audioplayers: ^6.1.0

  # Logging and Development
  logger: ^2.4.0

  # App Info
  package_info_plus: ^8.0.2

  # Desktop Window Management
  window_manager: ^0.3.9

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
