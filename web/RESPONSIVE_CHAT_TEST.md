# 响应式聊天界面测试指南

## 功能说明
Web客户端的聊天界面现在支持响应式布局：
- **移动端** (屏幕宽度 ≤ 768px): 输入框位于顶部
- **桌面端** (屏幕宽度 > 768px): 输入框位于底部

## 测试步骤

### 1. 启动开发服务器
```bash
cd web
bash start-dev.sh
```
服务器将在 http://localhost:9000 启动

### 2. 桌面端测试
1. 在浏览器中打开 http://localhost:9000
2. 登录并进入聊天界面
3. 点击在线用户打开聊天对话框
4. **验证**: 输入框应该位于对话框底部

### 3. 移动端测试
1. 打开浏览器开发者工具 (F12)
2. 切换到响应式设计模式 (Ctrl+Shift+M 或点击设备图标)
3. 选择移动设备预设 (如 iPhone, Android) 或手动设置宽度 ≤ 768px
4. 刷新页面
5. 点击在线用户打开聊天对话框
6. **验证**: 输入框应该位于对话框顶部 (在header下方，messages上方)

### 4. 响应式切换测试
1. 在桌面模式下打开聊天对话框
2. 调整浏览器窗口大小，从宽屏逐渐缩小到768px以下
3. **验证**: 输入框应该从底部自动移动到顶部
4. 再次扩大窗口到768px以上
5. **验证**: 输入框应该从顶部自动移动到底部

## 预期行为

### 桌面端 (> 768px)
```
┌─────────────────────┐
│      Chat Header    │
├─────────────────────┤
│                     │
│    Chat Messages    │
│                     │
├─────────────────────┤
│    Input Field      │ ← 底部
└─────────────────────┘
```

### 移动端 (≤ 768px)
```
┌─────────────────────┐
│      Chat Header    │
├─────────────────────┤
│    Input Field      │ ← 顶部
├─────────────────────┤
│                     │
│    Chat Messages    │
│                     │
└─────────────────────┘
```

## 技术实现
- 使用 `window.innerWidth` 检测屏幕宽度
- 响应式监听 `resize` 事件
- 条件渲染两个输入框组件 (`v-if="isMobile"` 和 `v-if="!isMobile"`)
- 不同的CSS类 (`chat-input-top` 和 `chat-input-bottom`) 处理边框样式

## 故障排除
如果响应式切换不工作：
1. 检查浏览器控制台是否有JavaScript错误
2. 确认 `isMobile` 响应式变量是否正确更新
3. 验证CSS类是否正确应用
4. 检查窗口resize事件监听器是否正常工作
