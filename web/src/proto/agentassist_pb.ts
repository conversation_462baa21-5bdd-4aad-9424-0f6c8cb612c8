// @generated by protoc-gen-es v2.5.2 with parameter "target=ts"
// @generated from file agentassist.proto (package agentassistproto, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file agentassist.proto.
 */
export const file_agentassist: GenFile = /*@__PURE__*/
  fileDesc("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");

/**
 * TextContent represents text provided to or from an LLM.
 * It must have Type set to "text".
 *
 * @generated from message agentassistproto.TextContent
 */
export type TextContent = Message<"agentassistproto.TextContent"> & {
  /**
   * Must be "text"
   *
   * @generated from field: string type = 1;
   */
  type: string;

  /**
   * The text content
   *
   * @generated from field: string text = 2;
   */
  text: string;
};

/**
 * Describes the message agentassistproto.TextContent.
 * Use `create(TextContentSchema)` to create a new message.
 */
export const TextContentSchema: GenMessage<TextContent> = /*@__PURE__*/
  messageDesc(file_agentassist, 0);

/**
 * ImageContent represents an image provided to or from an LLM.
 * It must have Type set to "image".
 *
 * @generated from message agentassistproto.ImageContent
 */
export type ImageContent = Message<"agentassistproto.ImageContent"> & {
  /**
   * Must be "image"
   *
   * @generated from field: string type = 1;
   */
  type: string;

  /**
   * Base64-encoded image data
   *
   * @generated from field: string data = 2;
   */
  data: string;

  /**
   * MIME type of the image
   *
   * @generated from field: string mime_type = 3;
   */
  mimeType: string;
};

/**
 * Describes the message agentassistproto.ImageContent.
 * Use `create(ImageContentSchema)` to create a new message.
 */
export const ImageContentSchema: GenMessage<ImageContent> = /*@__PURE__*/
  messageDesc(file_agentassist, 1);

/**
 * AudioContent represents audio data provided to or from an LLM.
 * It must have Type set to "audio".
 *
 * @generated from message agentassistproto.AudioContent
 */
export type AudioContent = Message<"agentassistproto.AudioContent"> & {
  /**
   * Must be "audio"
   *
   * @generated from field: string type = 1;
   */
  type: string;

  /**
   * Base64-encoded audio data
   *
   * @generated from field: string data = 2;
   */
  data: string;

  /**
   * MIME type of the audio
   *
   * @generated from field: string mime_type = 3;
   */
  mimeType: string;
};

/**
 * Describes the message agentassistproto.AudioContent.
 * Use `create(AudioContentSchema)` to create a new message.
 */
export const AudioContentSchema: GenMessage<AudioContent> = /*@__PURE__*/
  messageDesc(file_agentassist, 2);

/**
 * EmbeddedResource represents a resource embedded into a prompt or tool call
 * result. It must have Type set to "embedded_resource".
 *
 * @generated from message agentassistproto.EmbeddedResource
 */
export type EmbeddedResource = Message<"agentassistproto.EmbeddedResource"> & {
  /**
   * Must be "embedded_resource"
   *
   * @generated from field: string type = 1;
   */
  type: string;

  /**
   * URI of the embedded resource
   *
   * @generated from field: string uri = 2;
   */
  uri: string;

  /**
   * MIME type of the resource
   *
   * @generated from field: string mime_type = 3;
   */
  mimeType: string;

  /**
   * Optional: The actual resource data
   *
   * @generated from field: bytes data = 4;
   */
  data: Uint8Array;
};

/**
 * Describes the message agentassistproto.EmbeddedResource.
 * Use `create(EmbeddedResourceSchema)` to create a new message.
 */
export const EmbeddedResourceSchema: GenMessage<EmbeddedResource> = /*@__PURE__*/
  messageDesc(file_agentassist, 3);

/**
 * @generated from message agentassistproto.McpResultContent
 */
export type McpResultContent = Message<"agentassistproto.McpResultContent"> & {
  /**
   * content type
   * 1: text
   * 2: image
   * 3: audio
   * 4: embedded resource
   *
   * @generated from field: int32 type = 1;
   */
  type: number;

  /**
   * text
   *
   * @generated from field: agentassistproto.TextContent text = 2;
   */
  text?: TextContent;

  /**
   * image
   *
   * @generated from field: agentassistproto.ImageContent image = 3;
   */
  image?: ImageContent;

  /**
   * audio
   *
   * @generated from field: agentassistproto.AudioContent audio = 4;
   */
  audio?: AudioContent;

  /**
   * embedded resource
   *
   * @generated from field: agentassistproto.EmbeddedResource embedded_resource = 5;
   */
  embeddedResource?: EmbeddedResource;
};

/**
 * Describes the message agentassistproto.McpResultContent.
 * Use `create(McpResultContentSchema)` to create a new message.
 */
export const McpResultContentSchema: GenMessage<McpResultContent> = /*@__PURE__*/
  messageDesc(file_agentassist, 4);

/**
 * @generated from message agentassistproto.MsgEmpty
 */
export type MsgEmpty = Message<"agentassistproto.MsgEmpty"> & {
};

/**
 * Describes the message agentassistproto.MsgEmpty.
 * Use `create(MsgEmptySchema)` to create a new message.
 */
export const MsgEmptySchema: GenMessage<MsgEmpty> = /*@__PURE__*/
  messageDesc(file_agentassist, 5);

/**
 * @generated from message agentassistproto.McpAskQuestionRequest
 */
export type McpAskQuestionRequest = Message<"agentassistproto.McpAskQuestionRequest"> & {
  /**
   * current project directory
   *
   * @generated from field: string ProjectDirectory = 1;
   */
  ProjectDirectory: string;

  /**
   * ai agent's question
   *
   * @generated from field: string Question = 2;
   */
  Question: string;

  /**
   * timeout in seconds, default is 600s
   *
   * @generated from field: int32 Timeout = 3;
   */
  Timeout: number;
};

/**
 * Describes the message agentassistproto.McpAskQuestionRequest.
 * Use `create(McpAskQuestionRequestSchema)` to create a new message.
 */
export const McpAskQuestionRequestSchema: GenMessage<McpAskQuestionRequest> = /*@__PURE__*/
  messageDesc(file_agentassist, 6);

/**
 * @generated from message agentassistproto.AskQuestionRequest
 */
export type AskQuestionRequest = Message<"agentassistproto.AskQuestionRequest"> & {
  /**
   * request id
   *
   * @generated from field: string ID = 1;
   */
  ID: string;

  /**
   * user token
   *
   * @generated from field: string UserToken = 2;
   */
  UserToken: string;

  /**
   * ai agent's question
   *
   * @generated from field: agentassistproto.McpAskQuestionRequest Request = 3;
   */
  Request?: McpAskQuestionRequest;
};

/**
 * Describes the message agentassistproto.AskQuestionRequest.
 * Use `create(AskQuestionRequestSchema)` to create a new message.
 */
export const AskQuestionRequestSchema: GenMessage<AskQuestionRequest> = /*@__PURE__*/
  messageDesc(file_agentassist, 7);

/**
 * @generated from message agentassistproto.AskQuestionResponse
 */
export type AskQuestionResponse = Message<"agentassistproto.AskQuestionResponse"> & {
  /**
   * request id
   *
   * @generated from field: string ID = 1;
   */
  ID: string;

  /**
   * @generated from field: bool IsError = 2;
   */
  IsError: boolean;

  /**
   * @generated from field: map<string, string> Meta = 3;
   */
  Meta: { [key: string]: string };

  /**
   * @generated from field: repeated agentassistproto.McpResultContent contents = 4;
   */
  contents: McpResultContent[];
};

/**
 * Describes the message agentassistproto.AskQuestionResponse.
 * Use `create(AskQuestionResponseSchema)` to create a new message.
 */
export const AskQuestionResponseSchema: GenMessage<AskQuestionResponse> = /*@__PURE__*/
  messageDesc(file_agentassist, 8);

/**
 * @generated from message agentassistproto.McpTaskFinishRequest
 */
export type McpTaskFinishRequest = Message<"agentassistproto.McpTaskFinishRequest"> & {
  /**
   * current project directory
   *
   * @generated from field: string ProjectDirectory = 1;
   */
  ProjectDirectory: string;

  /**
   * ai agent's summary
   *
   * @generated from field: string Summary = 2;
   */
  Summary: string;

  /**
   * timeout in seconds, default is 600s
   *
   * @generated from field: int32 Timeout = 3;
   */
  Timeout: number;
};

/**
 * Describes the message agentassistproto.McpTaskFinishRequest.
 * Use `create(McpTaskFinishRequestSchema)` to create a new message.
 */
export const McpTaskFinishRequestSchema: GenMessage<McpTaskFinishRequest> = /*@__PURE__*/
  messageDesc(file_agentassist, 9);

/**
 * @generated from message agentassistproto.TaskFinishRequest
 */
export type TaskFinishRequest = Message<"agentassistproto.TaskFinishRequest"> & {
  /**
   * request id
   *
   * @generated from field: string ID = 1;
   */
  ID: string;

  /**
   * user token
   *
   * @generated from field: string UserToken = 2;
   */
  UserToken: string;

  /**
   * ai agent's summary
   *
   * @generated from field: agentassistproto.McpTaskFinishRequest Request = 3;
   */
  Request?: McpTaskFinishRequest;
};

/**
 * Describes the message agentassistproto.TaskFinishRequest.
 * Use `create(TaskFinishRequestSchema)` to create a new message.
 */
export const TaskFinishRequestSchema: GenMessage<TaskFinishRequest> = /*@__PURE__*/
  messageDesc(file_agentassist, 10);

/**
 * @generated from message agentassistproto.TaskFinishResponse
 */
export type TaskFinishResponse = Message<"agentassistproto.TaskFinishResponse"> & {
  /**
   * request id
   *
   * @generated from field: string ID = 1;
   */
  ID: string;

  /**
   * @generated from field: bool IsError = 2;
   */
  IsError: boolean;

  /**
   * @generated from field: map<string, string> Meta = 3;
   */
  Meta: { [key: string]: string };

  /**
   * @generated from field: repeated agentassistproto.McpResultContent contents = 4;
   */
  contents: McpResultContent[];
};

/**
 * Describes the message agentassistproto.TaskFinishResponse.
 * Use `create(TaskFinishResponseSchema)` to create a new message.
 */
export const TaskFinishResponseSchema: GenMessage<TaskFinishResponse> = /*@__PURE__*/
  messageDesc(file_agentassist, 11);

/**
 * @generated from message agentassistproto.CheckMessageValidityRequest
 */
export type CheckMessageValidityRequest = Message<"agentassistproto.CheckMessageValidityRequest"> & {
  /**
   * list of request IDs to check
   *
   * @generated from field: repeated string request_ids = 1;
   */
  requestIds: string[];
};

/**
 * Describes the message agentassistproto.CheckMessageValidityRequest.
 * Use `create(CheckMessageValidityRequestSchema)` to create a new message.
 */
export const CheckMessageValidityRequestSchema: GenMessage<CheckMessageValidityRequest> = /*@__PURE__*/
  messageDesc(file_agentassist, 12);

/**
 * @generated from message agentassistproto.CheckMessageValidityResponse
 */
export type CheckMessageValidityResponse = Message<"agentassistproto.CheckMessageValidityResponse"> & {
  /**
   * map of request ID to validity status
   *
   * @generated from field: map<string, bool> validity = 1;
   */
  validity: { [key: string]: boolean };
};

/**
 * Describes the message agentassistproto.CheckMessageValidityResponse.
 * Use `create(CheckMessageValidityResponseSchema)` to create a new message.
 */
export const CheckMessageValidityResponseSchema: GenMessage<CheckMessageValidityResponse> = /*@__PURE__*/
  messageDesc(file_agentassist, 13);

/**
 * GetPendingMessagesRequest represents a request to get all pending messages for a user
 *
 * @generated from message agentassistproto.GetPendingMessagesRequest
 */
export type GetPendingMessagesRequest = Message<"agentassistproto.GetPendingMessagesRequest"> & {
  /**
   * user token to filter messages
   *
   * @generated from field: string user_token = 1;
   */
  userToken: string;
};

/**
 * Describes the message agentassistproto.GetPendingMessagesRequest.
 * Use `create(GetPendingMessagesRequestSchema)` to create a new message.
 */
export const GetPendingMessagesRequestSchema: GenMessage<GetPendingMessagesRequest> = /*@__PURE__*/
  messageDesc(file_agentassist, 14);

/**
 * PendingMessage represents a single pending message
 *
 * @generated from message agentassistproto.PendingMessage
 */
export type PendingMessage = Message<"agentassistproto.PendingMessage"> & {
  /**
   * message type: "AskQuestion" or "TaskFinish"
   *
   * @generated from field: string message_type = 1;
   */
  messageType: string;

  /**
   * ask question request (if message_type is "AskQuestion")
   *
   * @generated from field: agentassistproto.AskQuestionRequest ask_question_request = 2;
   */
  askQuestionRequest?: AskQuestionRequest;

  /**
   * task finish request (if message_type is "TaskFinish")
   *
   * @generated from field: agentassistproto.TaskFinishRequest task_finish_request = 3;
   */
  taskFinishRequest?: TaskFinishRequest;

  /**
   * timestamp when the message was created
   *
   * @generated from field: int64 created_at = 4;
   */
  createdAt: bigint;

  /**
   * timeout in seconds
   *
   * @generated from field: int32 timeout = 5;
   */
  timeout: number;
};

/**
 * Describes the message agentassistproto.PendingMessage.
 * Use `create(PendingMessageSchema)` to create a new message.
 */
export const PendingMessageSchema: GenMessage<PendingMessage> = /*@__PURE__*/
  messageDesc(file_agentassist, 15);

/**
 * GetPendingMessagesResponse represents the response containing all pending messages
 *
 * @generated from message agentassistproto.GetPendingMessagesResponse
 */
export type GetPendingMessagesResponse = Message<"agentassistproto.GetPendingMessagesResponse"> & {
  /**
   * list of pending messages
   *
   * @generated from field: repeated agentassistproto.PendingMessage pending_messages = 1;
   */
  pendingMessages: PendingMessage[];

  /**
   * total count of pending messages
   *
   * @generated from field: int32 total_count = 2;
   */
  totalCount: number;
};

/**
 * Describes the message agentassistproto.GetPendingMessagesResponse.
 * Use `create(GetPendingMessagesResponseSchema)` to create a new message.
 */
export const GetPendingMessagesResponseSchema: GenMessage<GetPendingMessagesResponse> = /*@__PURE__*/
  messageDesc(file_agentassist, 16);

/**
 * RequestCancelledNotification represents a notification that a request has been cancelled
 *
 * @generated from message agentassistproto.RequestCancelledNotification
 */
export type RequestCancelledNotification = Message<"agentassistproto.RequestCancelledNotification"> & {
  /**
   * request id that was cancelled
   *
   * @generated from field: string request_id = 1;
   */
  requestId: string;

  /**
   * reason for cancellation
   *
   * @generated from field: string reason = 2;
   */
  reason: string;

  /**
   * message type: "AskQuestion" or "TaskFinish"
   *
   * @generated from field: string message_type = 3;
   */
  messageType: string;
};

/**
 * Describes the message agentassistproto.RequestCancelledNotification.
 * Use `create(RequestCancelledNotificationSchema)` to create a new message.
 */
export const RequestCancelledNotificationSchema: GenMessage<RequestCancelledNotification> = /*@__PURE__*/
  messageDesc(file_agentassist, 17);

/**
 * OnlineUser represents an online user with the same token
 *
 * @generated from message agentassistproto.OnlineUser
 */
export type OnlineUser = Message<"agentassistproto.OnlineUser"> & {
  /**
   * client id
   *
   * @generated from field: string client_id = 1;
   */
  clientId: string;

  /**
   * user nickname
   *
   * @generated from field: string nickname = 2;
   */
  nickname: string;

  /**
   * connection timestamp
   *
   * @generated from field: int64 connected_at = 3;
   */
  connectedAt: bigint;
};

/**
 * Describes the message agentassistproto.OnlineUser.
 * Use `create(OnlineUserSchema)` to create a new message.
 */
export const OnlineUserSchema: GenMessage<OnlineUser> = /*@__PURE__*/
  messageDesc(file_agentassist, 18);

/**
 * GetOnlineUsersRequest represents a request to get online users with the same token
 *
 * @generated from message agentassistproto.GetOnlineUsersRequest
 */
export type GetOnlineUsersRequest = Message<"agentassistproto.GetOnlineUsersRequest"> & {
  /**
   * user token to filter users
   *
   * @generated from field: string user_token = 1;
   */
  userToken: string;
};

/**
 * Describes the message agentassistproto.GetOnlineUsersRequest.
 * Use `create(GetOnlineUsersRequestSchema)` to create a new message.
 */
export const GetOnlineUsersRequestSchema: GenMessage<GetOnlineUsersRequest> = /*@__PURE__*/
  messageDesc(file_agentassist, 19);

/**
 * GetOnlineUsersResponse represents the response containing online users
 *
 * @generated from message agentassistproto.GetOnlineUsersResponse
 */
export type GetOnlineUsersResponse = Message<"agentassistproto.GetOnlineUsersResponse"> & {
  /**
   * list of online users
   *
   * @generated from field: repeated agentassistproto.OnlineUser online_users = 1;
   */
  onlineUsers: OnlineUser[];

  /**
   * total count of online users
   *
   * @generated from field: int32 total_count = 2;
   */
  totalCount: number;
};

/**
 * Describes the message agentassistproto.GetOnlineUsersResponse.
 * Use `create(GetOnlineUsersResponseSchema)` to create a new message.
 */
export const GetOnlineUsersResponseSchema: GenMessage<GetOnlineUsersResponse> = /*@__PURE__*/
  messageDesc(file_agentassist, 20);

/**
 * ChatMessage represents a chat message between users
 *
 * @generated from message agentassistproto.ChatMessage
 */
export type ChatMessage = Message<"agentassistproto.ChatMessage"> & {
  /**
   * message id
   *
   * @generated from field: string message_id = 1;
   */
  messageId: string;

  /**
   * sender client id
   *
   * @generated from field: string sender_client_id = 2;
   */
  senderClientId: string;

  /**
   * sender nickname
   *
   * @generated from field: string sender_nickname = 3;
   */
  senderNickname: string;

  /**
   * receiver client id
   *
   * @generated from field: string receiver_client_id = 4;
   */
  receiverClientId: string;

  /**
   * receiver nickname
   *
   * @generated from field: string receiver_nickname = 5;
   */
  receiverNickname: string;

  /**
   * message content
   *
   * @generated from field: string content = 6;
   */
  content: string;

  /**
   * timestamp when the message was sent
   *
   * @generated from field: int64 sent_at = 7;
   */
  sentAt: bigint;
};

/**
 * Describes the message agentassistproto.ChatMessage.
 * Use `create(ChatMessageSchema)` to create a new message.
 */
export const ChatMessageSchema: GenMessage<ChatMessage> = /*@__PURE__*/
  messageDesc(file_agentassist, 21);

/**
 * SendChatMessageRequest represents a request to send a chat message
 *
 * @generated from message agentassistproto.SendChatMessageRequest
 */
export type SendChatMessageRequest = Message<"agentassistproto.SendChatMessageRequest"> & {
  /**
   * receiver client id
   *
   * @generated from field: string receiver_client_id = 1;
   */
  receiverClientId: string;

  /**
   * message content
   *
   * @generated from field: string content = 2;
   */
  content: string;
};

/**
 * Describes the message agentassistproto.SendChatMessageRequest.
 * Use `create(SendChatMessageRequestSchema)` to create a new message.
 */
export const SendChatMessageRequestSchema: GenMessage<SendChatMessageRequest> = /*@__PURE__*/
  messageDesc(file_agentassist, 22);

/**
 * ChatMessageNotification represents a notification of a new chat message
 *
 * @generated from message agentassistproto.ChatMessageNotification
 */
export type ChatMessageNotification = Message<"agentassistproto.ChatMessageNotification"> & {
  /**
   * the chat message
   *
   * @generated from field: agentassistproto.ChatMessage chat_message = 1;
   */
  chatMessage?: ChatMessage;
};

/**
 * Describes the message agentassistproto.ChatMessageNotification.
 * Use `create(ChatMessageNotificationSchema)` to create a new message.
 */
export const ChatMessageNotificationSchema: GenMessage<ChatMessageNotification> = /*@__PURE__*/
  messageDesc(file_agentassist, 23);

/**
 * SendBackspaceRequest represents a request to send a backspace command
 *
 * @generated from message agentassistproto.SendBackspaceRequest
 */
export type SendBackspaceRequest = Message<"agentassistproto.SendBackspaceRequest"> & {
  /**
   * receiver client id
   *
   * @generated from field: string receiver_client_id = 1;
   */
  receiverClientId: string;
};

/**
 * Describes the message agentassistproto.SendBackspaceRequest.
 * Use `create(SendBackspaceRequestSchema)` to create a new message.
 */
export const SendBackspaceRequestSchema: GenMessage<SendBackspaceRequest> = /*@__PURE__*/
  messageDesc(file_agentassist, 24);

/**
 * UserLoginResponse represents the response to a user login
 *
 * @generated from message agentassistproto.UserLoginResponse
 */
export type UserLoginResponse = Message<"agentassistproto.UserLoginResponse"> & {
  /**
   * client id assigned by server
   *
   * @generated from field: string client_id = 1;
   */
  clientId: string;

  /**
   * success status
   *
   * @generated from field: bool success = 2;
   */
  success: boolean;

  /**
   * error message if login failed
   *
   * @generated from field: string error_message = 3;
   */
  errorMessage: string;
};

/**
 * Describes the message agentassistproto.UserLoginResponse.
 * Use `create(UserLoginResponseSchema)` to create a new message.
 */
export const UserLoginResponseSchema: GenMessage<UserLoginResponse> = /*@__PURE__*/
  messageDesc(file_agentassist, 25);

/**
 * UserConnectionStatusNotification represents a notification when a user connects or disconnects
 *
 * @generated from message agentassistproto.UserConnectionStatusNotification
 */
export type UserConnectionStatusNotification = Message<"agentassistproto.UserConnectionStatusNotification"> & {
  /**
   * the user who connected/disconnected
   *
   * @generated from field: agentassistproto.OnlineUser user = 1;
   */
  user?: OnlineUser;

  /**
   * connection status: "connected" or "disconnected"
   *
   * @generated from field: string status = 2;
   */
  status: string;

  /**
   * timestamp of the status change
   *
   * @generated from field: int64 timestamp = 3;
   */
  timestamp: bigint;
};

/**
 * Describes the message agentassistproto.UserConnectionStatusNotification.
 * Use `create(UserConnectionStatusNotificationSchema)` to create a new message.
 */
export const UserConnectionStatusNotificationSchema: GenMessage<UserConnectionStatusNotification> = /*@__PURE__*/
  messageDesc(file_agentassist, 26);

/**
 * @generated from message agentassistproto.WebsocketMessage
 */
export type WebsocketMessage = Message<"agentassistproto.WebsocketMessage"> & {
  /**
   * WebsocketMessage cmd
   * AskQuestion: mcp ask_question
   * TaskFinish: mcp task_finish
   * AskQuestionReply: user ask_question reply
   * TaskFinishReply: user task_finish reply
   * UserLogin: user login, str param is user token, nickname is user nickname
   * AskQuestionReplyNotification: notification of an AskQuestionReply
   * TaskFinishReplyNotification: notification of a TaskFinishReply
   * CheckMessageValidity: check if messages are still valid
   * GetPendingMessages: get all pending messages for a user
   * RequestCancelled: notification that a request has been cancelled
   * GetOnlineUsers: get online users with the same token
   * SendChatMessage: send a chat message to another user
   * ChatMessageNotification: notification of a new chat message
   * SendBackspace: send a backspace command to another user
   *
   * @generated from field: string Cmd = 1;
   */
  Cmd: string;

  /**
   * ask question
   *
   * @generated from field: agentassistproto.AskQuestionRequest AskQuestionRequest = 2;
   */
  AskQuestionRequest?: AskQuestionRequest;

  /**
   * task finish
   *
   * @generated from field: agentassistproto.TaskFinishRequest TaskFinishRequest = 3;
   */
  TaskFinishRequest?: TaskFinishRequest;

  /**
   * ask question reply
   *
   * @generated from field: agentassistproto.AskQuestionResponse AskQuestionResponse = 4;
   */
  AskQuestionResponse?: AskQuestionResponse;

  /**
   * task finish reply
   *
   * @generated from field: agentassistproto.TaskFinishResponse TaskFinishResponse = 5;
   */
  TaskFinishResponse?: TaskFinishResponse;

  /**
   * check message validity
   *
   * @generated from field: agentassistproto.CheckMessageValidityRequest CheckMessageValidityRequest = 13;
   */
  CheckMessageValidityRequest?: CheckMessageValidityRequest;

  /**
   * check message validity response
   *
   * @generated from field: agentassistproto.CheckMessageValidityResponse CheckMessageValidityResponse = 14;
   */
  CheckMessageValidityResponse?: CheckMessageValidityResponse;

  /**
   * get pending messages request
   *
   * @generated from field: agentassistproto.GetPendingMessagesRequest GetPendingMessagesRequest = 15;
   */
  GetPendingMessagesRequest?: GetPendingMessagesRequest;

  /**
   * get pending messages response
   *
   * @generated from field: agentassistproto.GetPendingMessagesResponse GetPendingMessagesResponse = 16;
   */
  GetPendingMessagesResponse?: GetPendingMessagesResponse;

  /**
   * request cancelled notification
   *
   * @generated from field: agentassistproto.RequestCancelledNotification RequestCancelledNotification = 17;
   */
  RequestCancelledNotification?: RequestCancelledNotification;

  /**
   * get online users request
   *
   * @generated from field: agentassistproto.GetOnlineUsersRequest GetOnlineUsersRequest = 19;
   */
  GetOnlineUsersRequest?: GetOnlineUsersRequest;

  /**
   * get online users response
   *
   * @generated from field: agentassistproto.GetOnlineUsersResponse GetOnlineUsersResponse = 20;
   */
  GetOnlineUsersResponse?: GetOnlineUsersResponse;

  /**
   * send chat message request
   *
   * @generated from field: agentassistproto.SendChatMessageRequest SendChatMessageRequest = 21;
   */
  SendChatMessageRequest?: SendChatMessageRequest;

  /**
   * chat message notification
   *
   * @generated from field: agentassistproto.ChatMessageNotification ChatMessageNotification = 22;
   */
  ChatMessageNotification?: ChatMessageNotification;

  /**
   * user login response
   *
   * @generated from field: agentassistproto.UserLoginResponse UserLoginResponse = 23;
   */
  UserLoginResponse?: UserLoginResponse;

  /**
   * user connection status notification
   *
   * @generated from field: agentassistproto.UserConnectionStatusNotification UserConnectionStatusNotification = 24;
   */
  UserConnectionStatusNotification?: UserConnectionStatusNotification;

  /**
   * send backspace request
   *
   * @generated from field: agentassistproto.SendBackspaceRequest SendBackspaceRequest = 25;
   */
  SendBackspaceRequest?: SendBackspaceRequest;

  /**
   * str param
   *
   * @generated from field: string StrParam = 12;
   */
  StrParam: string;

  /**
   * user nickname (for UserLogin and notifications)
   *
   * @generated from field: string Nickname = 18;
   */
  Nickname: string;
};

/**
 * Describes the message agentassistproto.WebsocketMessage.
 * Use `create(WebsocketMessageSchema)` to create a new message.
 */
export const WebsocketMessageSchema: GenMessage<WebsocketMessage> = /*@__PURE__*/
  messageDesc(file_agentassist, 27);

/**
 * @generated from service agentassistproto.SrvAgentAssist
 */
export const SrvAgentAssist: GenService<{
  /**
   * @generated from rpc agentassistproto.SrvAgentAssist.AskQuestion
   */
  askQuestion: {
    methodKind: "unary";
    input: typeof AskQuestionRequestSchema;
    output: typeof AskQuestionResponseSchema;
  },
  /**
   * @generated from rpc agentassistproto.SrvAgentAssist.TaskFinish
   */
  taskFinish: {
    methodKind: "unary";
    input: typeof TaskFinishRequestSchema;
    output: typeof TaskFinishResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_agentassist, 0);

