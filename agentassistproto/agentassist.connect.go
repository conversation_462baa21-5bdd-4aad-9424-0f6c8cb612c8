// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: agentassist.proto

package agentassistproto

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// SrvAgentAssistName is the fully-qualified name of the SrvAgentAssist service.
	SrvAgentAssistName = "agentassistproto.SrvAgentAssist"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// SrvAgentAssistAskQuestionProcedure is the fully-qualified name of the SrvAgentAssist's
	// AskQuestion RPC.
	SrvAgentAssistAskQuestionProcedure = "/agentassistproto.SrvAgentAssist/AskQuestion"
	// SrvAgentAssistTaskFinishProcedure is the fully-qualified name of the SrvAgentAssist's TaskFinish
	// RPC.
	SrvAgentAssistTaskFinishProcedure = "/agentassistproto.SrvAgentAssist/TaskFinish"
)

// SrvAgentAssistClient is a client for the agentassistproto.SrvAgentAssist service.
type SrvAgentAssistClient interface {
	AskQuestion(context.Context, *connect.Request[AskQuestionRequest]) (*connect.Response[AskQuestionResponse], error)
	TaskFinish(context.Context, *connect.Request[TaskFinishRequest]) (*connect.Response[TaskFinishResponse], error)
}

// NewSrvAgentAssistClient constructs a client for the agentassistproto.SrvAgentAssist service. By
// default, it uses the Connect protocol with the binary Protobuf Codec, asks for gzipped responses,
// and sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply the
// connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewSrvAgentAssistClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) SrvAgentAssistClient {
	baseURL = strings.TrimRight(baseURL, "/")
	srvAgentAssistMethods := File_agentassist_proto.Services().ByName("SrvAgentAssist").Methods()
	return &srvAgentAssistClient{
		askQuestion: connect.NewClient[AskQuestionRequest, AskQuestionResponse](
			httpClient,
			baseURL+SrvAgentAssistAskQuestionProcedure,
			connect.WithSchema(srvAgentAssistMethods.ByName("AskQuestion")),
			connect.WithClientOptions(opts...),
		),
		taskFinish: connect.NewClient[TaskFinishRequest, TaskFinishResponse](
			httpClient,
			baseURL+SrvAgentAssistTaskFinishProcedure,
			connect.WithSchema(srvAgentAssistMethods.ByName("TaskFinish")),
			connect.WithClientOptions(opts...),
		),
	}
}

// srvAgentAssistClient implements SrvAgentAssistClient.
type srvAgentAssistClient struct {
	askQuestion *connect.Client[AskQuestionRequest, AskQuestionResponse]
	taskFinish  *connect.Client[TaskFinishRequest, TaskFinishResponse]
}

// AskQuestion calls agentassistproto.SrvAgentAssist.AskQuestion.
func (c *srvAgentAssistClient) AskQuestion(ctx context.Context, req *connect.Request[AskQuestionRequest]) (*connect.Response[AskQuestionResponse], error) {
	return c.askQuestion.CallUnary(ctx, req)
}

// TaskFinish calls agentassistproto.SrvAgentAssist.TaskFinish.
func (c *srvAgentAssistClient) TaskFinish(ctx context.Context, req *connect.Request[TaskFinishRequest]) (*connect.Response[TaskFinishResponse], error) {
	return c.taskFinish.CallUnary(ctx, req)
}

// SrvAgentAssistHandler is an implementation of the agentassistproto.SrvAgentAssist service.
type SrvAgentAssistHandler interface {
	AskQuestion(context.Context, *connect.Request[AskQuestionRequest]) (*connect.Response[AskQuestionResponse], error)
	TaskFinish(context.Context, *connect.Request[TaskFinishRequest]) (*connect.Response[TaskFinishResponse], error)
}

// NewSrvAgentAssistHandler builds an HTTP handler from the service implementation. It returns the
// path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewSrvAgentAssistHandler(svc SrvAgentAssistHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	srvAgentAssistMethods := File_agentassist_proto.Services().ByName("SrvAgentAssist").Methods()
	srvAgentAssistAskQuestionHandler := connect.NewUnaryHandler(
		SrvAgentAssistAskQuestionProcedure,
		svc.AskQuestion,
		connect.WithSchema(srvAgentAssistMethods.ByName("AskQuestion")),
		connect.WithHandlerOptions(opts...),
	)
	srvAgentAssistTaskFinishHandler := connect.NewUnaryHandler(
		SrvAgentAssistTaskFinishProcedure,
		svc.TaskFinish,
		connect.WithSchema(srvAgentAssistMethods.ByName("TaskFinish")),
		connect.WithHandlerOptions(opts...),
	)
	return "/agentassistproto.SrvAgentAssist/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case SrvAgentAssistAskQuestionProcedure:
			srvAgentAssistAskQuestionHandler.ServeHTTP(w, r)
		case SrvAgentAssistTaskFinishProcedure:
			srvAgentAssistTaskFinishHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedSrvAgentAssistHandler returns CodeUnimplemented from all methods.
type UnimplementedSrvAgentAssistHandler struct{}

func (UnimplementedSrvAgentAssistHandler) AskQuestion(context.Context, *connect.Request[AskQuestionRequest]) (*connect.Response[AskQuestionResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("agentassistproto.SrvAgentAssist.AskQuestion is not implemented"))
}

func (UnimplementedSrvAgentAssistHandler) TaskFinish(context.Context, *connect.Request[TaskFinishRequest]) (*connect.Response[TaskFinishResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("agentassistproto.SrvAgentAssist.TaskFinish is not implemented"))
}
