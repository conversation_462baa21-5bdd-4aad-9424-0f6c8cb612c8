// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: agentassist.proto

package agentassistproto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// TextContent represents text provided to or from an LLM.
// It must have Type set to "text".
type TextContent struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          string                 `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"` // Must be "text"
	Text          string                 `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"` // The text content
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TextContent) Reset() {
	*x = TextContent{}
	mi := &file_agentassist_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TextContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TextContent) ProtoMessage() {}

func (x *TextContent) ProtoReflect() protoreflect.Message {
	mi := &file_agentassist_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TextContent.ProtoReflect.Descriptor instead.
func (*TextContent) Descriptor() ([]byte, []int) {
	return file_agentassist_proto_rawDescGZIP(), []int{0}
}

func (x *TextContent) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *TextContent) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

// ImageContent represents an image provided to or from an LLM.
// It must have Type set to "image".
type ImageContent struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          string                 `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`                         // Must be "image"
	Data          string                 `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`                         // Base64-encoded image data
	MimeType      string                 `protobuf:"bytes,3,opt,name=mime_type,json=mimeType,proto3" json:"mime_type,omitempty"` // MIME type of the image
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ImageContent) Reset() {
	*x = ImageContent{}
	mi := &file_agentassist_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ImageContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImageContent) ProtoMessage() {}

func (x *ImageContent) ProtoReflect() protoreflect.Message {
	mi := &file_agentassist_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImageContent.ProtoReflect.Descriptor instead.
func (*ImageContent) Descriptor() ([]byte, []int) {
	return file_agentassist_proto_rawDescGZIP(), []int{1}
}

func (x *ImageContent) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *ImageContent) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

func (x *ImageContent) GetMimeType() string {
	if x != nil {
		return x.MimeType
	}
	return ""
}

// AudioContent represents audio data provided to or from an LLM.
// It must have Type set to "audio".
type AudioContent struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          string                 `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`                         // Must be "audio"
	Data          string                 `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`                         // Base64-encoded audio data
	MimeType      string                 `protobuf:"bytes,3,opt,name=mime_type,json=mimeType,proto3" json:"mime_type,omitempty"` // MIME type of the audio
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AudioContent) Reset() {
	*x = AudioContent{}
	mi := &file_agentassist_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AudioContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AudioContent) ProtoMessage() {}

func (x *AudioContent) ProtoReflect() protoreflect.Message {
	mi := &file_agentassist_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AudioContent.ProtoReflect.Descriptor instead.
func (*AudioContent) Descriptor() ([]byte, []int) {
	return file_agentassist_proto_rawDescGZIP(), []int{2}
}

func (x *AudioContent) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *AudioContent) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

func (x *AudioContent) GetMimeType() string {
	if x != nil {
		return x.MimeType
	}
	return ""
}

// EmbeddedResource represents a resource embedded into a prompt or tool call
// result. It must have Type set to "embedded_resource".
type EmbeddedResource struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          string                 `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`                         // Must be "embedded_resource"
	Uri           string                 `protobuf:"bytes,2,opt,name=uri,proto3" json:"uri,omitempty"`                           // URI of the embedded resource
	MimeType      string                 `protobuf:"bytes,3,opt,name=mime_type,json=mimeType,proto3" json:"mime_type,omitempty"` // MIME type of the resource
	Data          []byte                 `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`                         // Optional: The actual resource data
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EmbeddedResource) Reset() {
	*x = EmbeddedResource{}
	mi := &file_agentassist_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EmbeddedResource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmbeddedResource) ProtoMessage() {}

func (x *EmbeddedResource) ProtoReflect() protoreflect.Message {
	mi := &file_agentassist_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmbeddedResource.ProtoReflect.Descriptor instead.
func (*EmbeddedResource) Descriptor() ([]byte, []int) {
	return file_agentassist_proto_rawDescGZIP(), []int{3}
}

func (x *EmbeddedResource) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *EmbeddedResource) GetUri() string {
	if x != nil {
		return x.Uri
	}
	return ""
}

func (x *EmbeddedResource) GetMimeType() string {
	if x != nil {
		return x.MimeType
	}
	return ""
}

func (x *EmbeddedResource) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

type McpResultContent struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// content type
	// 1: text
	// 2: image
	// 3: audio
	// 4: embedded resource
	Type int32 `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	// text
	Text *TextContent `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	// image
	Image *ImageContent `protobuf:"bytes,3,opt,name=image,proto3" json:"image,omitempty"`
	// audio
	Audio *AudioContent `protobuf:"bytes,4,opt,name=audio,proto3" json:"audio,omitempty"`
	// embedded resource
	EmbeddedResource *EmbeddedResource `protobuf:"bytes,5,opt,name=embedded_resource,json=embeddedResource,proto3" json:"embedded_resource,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *McpResultContent) Reset() {
	*x = McpResultContent{}
	mi := &file_agentassist_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *McpResultContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*McpResultContent) ProtoMessage() {}

func (x *McpResultContent) ProtoReflect() protoreflect.Message {
	mi := &file_agentassist_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use McpResultContent.ProtoReflect.Descriptor instead.
func (*McpResultContent) Descriptor() ([]byte, []int) {
	return file_agentassist_proto_rawDescGZIP(), []int{4}
}

func (x *McpResultContent) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *McpResultContent) GetText() *TextContent {
	if x != nil {
		return x.Text
	}
	return nil
}

func (x *McpResultContent) GetImage() *ImageContent {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *McpResultContent) GetAudio() *AudioContent {
	if x != nil {
		return x.Audio
	}
	return nil
}

func (x *McpResultContent) GetEmbeddedResource() *EmbeddedResource {
	if x != nil {
		return x.EmbeddedResource
	}
	return nil
}

type MsgEmpty struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MsgEmpty) Reset() {
	*x = MsgEmpty{}
	mi := &file_agentassist_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MsgEmpty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MsgEmpty) ProtoMessage() {}

func (x *MsgEmpty) ProtoReflect() protoreflect.Message {
	mi := &file_agentassist_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MsgEmpty.ProtoReflect.Descriptor instead.
func (*MsgEmpty) Descriptor() ([]byte, []int) {
	return file_agentassist_proto_rawDescGZIP(), []int{5}
}

type McpAskQuestionRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// current project directory
	ProjectDirectory string `protobuf:"bytes,1,opt,name=ProjectDirectory,proto3" json:"ProjectDirectory,omitempty"`
	// ai agent's question
	Question string `protobuf:"bytes,2,opt,name=Question,proto3" json:"Question,omitempty"`
	// timeout in seconds, default is 600s
	Timeout       int32 `protobuf:"varint,3,opt,name=Timeout,proto3" json:"Timeout,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *McpAskQuestionRequest) Reset() {
	*x = McpAskQuestionRequest{}
	mi := &file_agentassist_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *McpAskQuestionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*McpAskQuestionRequest) ProtoMessage() {}

func (x *McpAskQuestionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_agentassist_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use McpAskQuestionRequest.ProtoReflect.Descriptor instead.
func (*McpAskQuestionRequest) Descriptor() ([]byte, []int) {
	return file_agentassist_proto_rawDescGZIP(), []int{6}
}

func (x *McpAskQuestionRequest) GetProjectDirectory() string {
	if x != nil {
		return x.ProjectDirectory
	}
	return ""
}

func (x *McpAskQuestionRequest) GetQuestion() string {
	if x != nil {
		return x.Question
	}
	return ""
}

func (x *McpAskQuestionRequest) GetTimeout() int32 {
	if x != nil {
		return x.Timeout
	}
	return 0
}

type AskQuestionRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// request id
	ID string `protobuf:"bytes,1,opt,name=ID,proto3" json:"ID,omitempty"`
	// user token
	UserToken string `protobuf:"bytes,2,opt,name=UserToken,proto3" json:"UserToken,omitempty"`
	// ai agent's question
	Request       *McpAskQuestionRequest `protobuf:"bytes,3,opt,name=Request,proto3" json:"Request,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AskQuestionRequest) Reset() {
	*x = AskQuestionRequest{}
	mi := &file_agentassist_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AskQuestionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AskQuestionRequest) ProtoMessage() {}

func (x *AskQuestionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_agentassist_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AskQuestionRequest.ProtoReflect.Descriptor instead.
func (*AskQuestionRequest) Descriptor() ([]byte, []int) {
	return file_agentassist_proto_rawDescGZIP(), []int{7}
}

func (x *AskQuestionRequest) GetID() string {
	if x != nil {
		return x.ID
	}
	return ""
}

func (x *AskQuestionRequest) GetUserToken() string {
	if x != nil {
		return x.UserToken
	}
	return ""
}

func (x *AskQuestionRequest) GetRequest() *McpAskQuestionRequest {
	if x != nil {
		return x.Request
	}
	return nil
}

type AskQuestionResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// request id
	ID            string              `protobuf:"bytes,1,opt,name=ID,proto3" json:"ID,omitempty"`
	IsError       bool                `protobuf:"varint,2,opt,name=IsError,proto3" json:"IsError,omitempty"`
	Meta          map[string]string   `protobuf:"bytes,3,rep,name=Meta,proto3" json:"Meta,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Contents      []*McpResultContent `protobuf:"bytes,4,rep,name=contents,proto3" json:"contents,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AskQuestionResponse) Reset() {
	*x = AskQuestionResponse{}
	mi := &file_agentassist_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AskQuestionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AskQuestionResponse) ProtoMessage() {}

func (x *AskQuestionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_agentassist_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AskQuestionResponse.ProtoReflect.Descriptor instead.
func (*AskQuestionResponse) Descriptor() ([]byte, []int) {
	return file_agentassist_proto_rawDescGZIP(), []int{8}
}

func (x *AskQuestionResponse) GetID() string {
	if x != nil {
		return x.ID
	}
	return ""
}

func (x *AskQuestionResponse) GetIsError() bool {
	if x != nil {
		return x.IsError
	}
	return false
}

func (x *AskQuestionResponse) GetMeta() map[string]string {
	if x != nil {
		return x.Meta
	}
	return nil
}

func (x *AskQuestionResponse) GetContents() []*McpResultContent {
	if x != nil {
		return x.Contents
	}
	return nil
}

type McpTaskFinishRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// current project directory
	ProjectDirectory string `protobuf:"bytes,1,opt,name=ProjectDirectory,proto3" json:"ProjectDirectory,omitempty"`
	// ai agent's summary
	Summary string `protobuf:"bytes,2,opt,name=Summary,proto3" json:"Summary,omitempty"`
	// timeout in seconds, default is 600s
	Timeout       int32 `protobuf:"varint,3,opt,name=Timeout,proto3" json:"Timeout,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *McpTaskFinishRequest) Reset() {
	*x = McpTaskFinishRequest{}
	mi := &file_agentassist_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *McpTaskFinishRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*McpTaskFinishRequest) ProtoMessage() {}

func (x *McpTaskFinishRequest) ProtoReflect() protoreflect.Message {
	mi := &file_agentassist_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use McpTaskFinishRequest.ProtoReflect.Descriptor instead.
func (*McpTaskFinishRequest) Descriptor() ([]byte, []int) {
	return file_agentassist_proto_rawDescGZIP(), []int{9}
}

func (x *McpTaskFinishRequest) GetProjectDirectory() string {
	if x != nil {
		return x.ProjectDirectory
	}
	return ""
}

func (x *McpTaskFinishRequest) GetSummary() string {
	if x != nil {
		return x.Summary
	}
	return ""
}

func (x *McpTaskFinishRequest) GetTimeout() int32 {
	if x != nil {
		return x.Timeout
	}
	return 0
}

type TaskFinishRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// request id
	ID string `protobuf:"bytes,1,opt,name=ID,proto3" json:"ID,omitempty"`
	// user token
	UserToken string `protobuf:"bytes,2,opt,name=UserToken,proto3" json:"UserToken,omitempty"`
	// ai agent's summary
	Request       *McpTaskFinishRequest `protobuf:"bytes,3,opt,name=Request,proto3" json:"Request,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TaskFinishRequest) Reset() {
	*x = TaskFinishRequest{}
	mi := &file_agentassist_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TaskFinishRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskFinishRequest) ProtoMessage() {}

func (x *TaskFinishRequest) ProtoReflect() protoreflect.Message {
	mi := &file_agentassist_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskFinishRequest.ProtoReflect.Descriptor instead.
func (*TaskFinishRequest) Descriptor() ([]byte, []int) {
	return file_agentassist_proto_rawDescGZIP(), []int{10}
}

func (x *TaskFinishRequest) GetID() string {
	if x != nil {
		return x.ID
	}
	return ""
}

func (x *TaskFinishRequest) GetUserToken() string {
	if x != nil {
		return x.UserToken
	}
	return ""
}

func (x *TaskFinishRequest) GetRequest() *McpTaskFinishRequest {
	if x != nil {
		return x.Request
	}
	return nil
}

type TaskFinishResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// request id
	ID            string              `protobuf:"bytes,1,opt,name=ID,proto3" json:"ID,omitempty"`
	IsError       bool                `protobuf:"varint,2,opt,name=IsError,proto3" json:"IsError,omitempty"`
	Meta          map[string]string   `protobuf:"bytes,3,rep,name=Meta,proto3" json:"Meta,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Contents      []*McpResultContent `protobuf:"bytes,4,rep,name=contents,proto3" json:"contents,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TaskFinishResponse) Reset() {
	*x = TaskFinishResponse{}
	mi := &file_agentassist_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TaskFinishResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskFinishResponse) ProtoMessage() {}

func (x *TaskFinishResponse) ProtoReflect() protoreflect.Message {
	mi := &file_agentassist_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskFinishResponse.ProtoReflect.Descriptor instead.
func (*TaskFinishResponse) Descriptor() ([]byte, []int) {
	return file_agentassist_proto_rawDescGZIP(), []int{11}
}

func (x *TaskFinishResponse) GetID() string {
	if x != nil {
		return x.ID
	}
	return ""
}

func (x *TaskFinishResponse) GetIsError() bool {
	if x != nil {
		return x.IsError
	}
	return false
}

func (x *TaskFinishResponse) GetMeta() map[string]string {
	if x != nil {
		return x.Meta
	}
	return nil
}

func (x *TaskFinishResponse) GetContents() []*McpResultContent {
	if x != nil {
		return x.Contents
	}
	return nil
}

type CheckMessageValidityRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// list of request IDs to check
	RequestIds    []string `protobuf:"bytes,1,rep,name=request_ids,json=requestIds,proto3" json:"request_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckMessageValidityRequest) Reset() {
	*x = CheckMessageValidityRequest{}
	mi := &file_agentassist_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckMessageValidityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckMessageValidityRequest) ProtoMessage() {}

func (x *CheckMessageValidityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_agentassist_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckMessageValidityRequest.ProtoReflect.Descriptor instead.
func (*CheckMessageValidityRequest) Descriptor() ([]byte, []int) {
	return file_agentassist_proto_rawDescGZIP(), []int{12}
}

func (x *CheckMessageValidityRequest) GetRequestIds() []string {
	if x != nil {
		return x.RequestIds
	}
	return nil
}

type CheckMessageValidityResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// map of request ID to validity status
	Validity      map[string]bool `protobuf:"bytes,1,rep,name=validity,proto3" json:"validity,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckMessageValidityResponse) Reset() {
	*x = CheckMessageValidityResponse{}
	mi := &file_agentassist_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckMessageValidityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckMessageValidityResponse) ProtoMessage() {}

func (x *CheckMessageValidityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_agentassist_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckMessageValidityResponse.ProtoReflect.Descriptor instead.
func (*CheckMessageValidityResponse) Descriptor() ([]byte, []int) {
	return file_agentassist_proto_rawDescGZIP(), []int{13}
}

func (x *CheckMessageValidityResponse) GetValidity() map[string]bool {
	if x != nil {
		return x.Validity
	}
	return nil
}

// GetPendingMessagesRequest represents a request to get all pending messages for a user
type GetPendingMessagesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// user token to filter messages
	UserToken     string `protobuf:"bytes,1,opt,name=user_token,json=userToken,proto3" json:"user_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPendingMessagesRequest) Reset() {
	*x = GetPendingMessagesRequest{}
	mi := &file_agentassist_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPendingMessagesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPendingMessagesRequest) ProtoMessage() {}

func (x *GetPendingMessagesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_agentassist_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPendingMessagesRequest.ProtoReflect.Descriptor instead.
func (*GetPendingMessagesRequest) Descriptor() ([]byte, []int) {
	return file_agentassist_proto_rawDescGZIP(), []int{14}
}

func (x *GetPendingMessagesRequest) GetUserToken() string {
	if x != nil {
		return x.UserToken
	}
	return ""
}

// PendingMessage represents a single pending message
type PendingMessage struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// message type: "AskQuestion" or "TaskFinish"
	MessageType string `protobuf:"bytes,1,opt,name=message_type,json=messageType,proto3" json:"message_type,omitempty"`
	// ask question request (if message_type is "AskQuestion")
	AskQuestionRequest *AskQuestionRequest `protobuf:"bytes,2,opt,name=ask_question_request,json=askQuestionRequest,proto3" json:"ask_question_request,omitempty"`
	// task finish request (if message_type is "TaskFinish")
	TaskFinishRequest *TaskFinishRequest `protobuf:"bytes,3,opt,name=task_finish_request,json=taskFinishRequest,proto3" json:"task_finish_request,omitempty"`
	// timestamp when the message was created
	CreatedAt int64 `protobuf:"varint,4,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// timeout in seconds
	Timeout       int32 `protobuf:"varint,5,opt,name=timeout,proto3" json:"timeout,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PendingMessage) Reset() {
	*x = PendingMessage{}
	mi := &file_agentassist_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PendingMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PendingMessage) ProtoMessage() {}

func (x *PendingMessage) ProtoReflect() protoreflect.Message {
	mi := &file_agentassist_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PendingMessage.ProtoReflect.Descriptor instead.
func (*PendingMessage) Descriptor() ([]byte, []int) {
	return file_agentassist_proto_rawDescGZIP(), []int{15}
}

func (x *PendingMessage) GetMessageType() string {
	if x != nil {
		return x.MessageType
	}
	return ""
}

func (x *PendingMessage) GetAskQuestionRequest() *AskQuestionRequest {
	if x != nil {
		return x.AskQuestionRequest
	}
	return nil
}

func (x *PendingMessage) GetTaskFinishRequest() *TaskFinishRequest {
	if x != nil {
		return x.TaskFinishRequest
	}
	return nil
}

func (x *PendingMessage) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *PendingMessage) GetTimeout() int32 {
	if x != nil {
		return x.Timeout
	}
	return 0
}

// GetPendingMessagesResponse represents the response containing all pending messages
type GetPendingMessagesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// list of pending messages
	PendingMessages []*PendingMessage `protobuf:"bytes,1,rep,name=pending_messages,json=pendingMessages,proto3" json:"pending_messages,omitempty"`
	// total count of pending messages
	TotalCount    int32 `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPendingMessagesResponse) Reset() {
	*x = GetPendingMessagesResponse{}
	mi := &file_agentassist_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPendingMessagesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPendingMessagesResponse) ProtoMessage() {}

func (x *GetPendingMessagesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_agentassist_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPendingMessagesResponse.ProtoReflect.Descriptor instead.
func (*GetPendingMessagesResponse) Descriptor() ([]byte, []int) {
	return file_agentassist_proto_rawDescGZIP(), []int{16}
}

func (x *GetPendingMessagesResponse) GetPendingMessages() []*PendingMessage {
	if x != nil {
		return x.PendingMessages
	}
	return nil
}

func (x *GetPendingMessagesResponse) GetTotalCount() int32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

// RequestCancelledNotification represents a notification that a request has been cancelled
type RequestCancelledNotification struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// request id that was cancelled
	RequestId string `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// reason for cancellation
	Reason string `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`
	// message type: "AskQuestion" or "TaskFinish"
	MessageType   string `protobuf:"bytes,3,opt,name=message_type,json=messageType,proto3" json:"message_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RequestCancelledNotification) Reset() {
	*x = RequestCancelledNotification{}
	mi := &file_agentassist_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RequestCancelledNotification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequestCancelledNotification) ProtoMessage() {}

func (x *RequestCancelledNotification) ProtoReflect() protoreflect.Message {
	mi := &file_agentassist_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequestCancelledNotification.ProtoReflect.Descriptor instead.
func (*RequestCancelledNotification) Descriptor() ([]byte, []int) {
	return file_agentassist_proto_rawDescGZIP(), []int{17}
}

func (x *RequestCancelledNotification) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *RequestCancelledNotification) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *RequestCancelledNotification) GetMessageType() string {
	if x != nil {
		return x.MessageType
	}
	return ""
}

// OnlineUser represents an online user with the same token
type OnlineUser struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// client id
	ClientId string `protobuf:"bytes,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	// user nickname
	Nickname string `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	// connection timestamp
	ConnectedAt   int64 `protobuf:"varint,3,opt,name=connected_at,json=connectedAt,proto3" json:"connected_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OnlineUser) Reset() {
	*x = OnlineUser{}
	mi := &file_agentassist_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OnlineUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OnlineUser) ProtoMessage() {}

func (x *OnlineUser) ProtoReflect() protoreflect.Message {
	mi := &file_agentassist_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OnlineUser.ProtoReflect.Descriptor instead.
func (*OnlineUser) Descriptor() ([]byte, []int) {
	return file_agentassist_proto_rawDescGZIP(), []int{18}
}

func (x *OnlineUser) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *OnlineUser) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *OnlineUser) GetConnectedAt() int64 {
	if x != nil {
		return x.ConnectedAt
	}
	return 0
}

// GetOnlineUsersRequest represents a request to get online users with the same token
type GetOnlineUsersRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// user token to filter users
	UserToken     string `protobuf:"bytes,1,opt,name=user_token,json=userToken,proto3" json:"user_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetOnlineUsersRequest) Reset() {
	*x = GetOnlineUsersRequest{}
	mi := &file_agentassist_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOnlineUsersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOnlineUsersRequest) ProtoMessage() {}

func (x *GetOnlineUsersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_agentassist_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOnlineUsersRequest.ProtoReflect.Descriptor instead.
func (*GetOnlineUsersRequest) Descriptor() ([]byte, []int) {
	return file_agentassist_proto_rawDescGZIP(), []int{19}
}

func (x *GetOnlineUsersRequest) GetUserToken() string {
	if x != nil {
		return x.UserToken
	}
	return ""
}

// GetOnlineUsersResponse represents the response containing online users
type GetOnlineUsersResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// list of online users
	OnlineUsers []*OnlineUser `protobuf:"bytes,1,rep,name=online_users,json=onlineUsers,proto3" json:"online_users,omitempty"`
	// total count of online users
	TotalCount    int32 `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetOnlineUsersResponse) Reset() {
	*x = GetOnlineUsersResponse{}
	mi := &file_agentassist_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOnlineUsersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOnlineUsersResponse) ProtoMessage() {}

func (x *GetOnlineUsersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_agentassist_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOnlineUsersResponse.ProtoReflect.Descriptor instead.
func (*GetOnlineUsersResponse) Descriptor() ([]byte, []int) {
	return file_agentassist_proto_rawDescGZIP(), []int{20}
}

func (x *GetOnlineUsersResponse) GetOnlineUsers() []*OnlineUser {
	if x != nil {
		return x.OnlineUsers
	}
	return nil
}

func (x *GetOnlineUsersResponse) GetTotalCount() int32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

// ChatMessage represents a chat message between users
type ChatMessage struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// message id
	MessageId string `protobuf:"bytes,1,opt,name=message_id,json=messageId,proto3" json:"message_id,omitempty"`
	// sender client id
	SenderClientId string `protobuf:"bytes,2,opt,name=sender_client_id,json=senderClientId,proto3" json:"sender_client_id,omitempty"`
	// sender nickname
	SenderNickname string `protobuf:"bytes,3,opt,name=sender_nickname,json=senderNickname,proto3" json:"sender_nickname,omitempty"`
	// receiver client id
	ReceiverClientId string `protobuf:"bytes,4,opt,name=receiver_client_id,json=receiverClientId,proto3" json:"receiver_client_id,omitempty"`
	// receiver nickname
	ReceiverNickname string `protobuf:"bytes,5,opt,name=receiver_nickname,json=receiverNickname,proto3" json:"receiver_nickname,omitempty"`
	// message content
	Content string `protobuf:"bytes,6,opt,name=content,proto3" json:"content,omitempty"`
	// timestamp when the message was sent
	SentAt        int64 `protobuf:"varint,7,opt,name=sent_at,json=sentAt,proto3" json:"sent_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChatMessage) Reset() {
	*x = ChatMessage{}
	mi := &file_agentassist_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChatMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatMessage) ProtoMessage() {}

func (x *ChatMessage) ProtoReflect() protoreflect.Message {
	mi := &file_agentassist_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatMessage.ProtoReflect.Descriptor instead.
func (*ChatMessage) Descriptor() ([]byte, []int) {
	return file_agentassist_proto_rawDescGZIP(), []int{21}
}

func (x *ChatMessage) GetMessageId() string {
	if x != nil {
		return x.MessageId
	}
	return ""
}

func (x *ChatMessage) GetSenderClientId() string {
	if x != nil {
		return x.SenderClientId
	}
	return ""
}

func (x *ChatMessage) GetSenderNickname() string {
	if x != nil {
		return x.SenderNickname
	}
	return ""
}

func (x *ChatMessage) GetReceiverClientId() string {
	if x != nil {
		return x.ReceiverClientId
	}
	return ""
}

func (x *ChatMessage) GetReceiverNickname() string {
	if x != nil {
		return x.ReceiverNickname
	}
	return ""
}

func (x *ChatMessage) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *ChatMessage) GetSentAt() int64 {
	if x != nil {
		return x.SentAt
	}
	return 0
}

// SendChatMessageRequest represents a request to send a chat message
type SendChatMessageRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// receiver client id
	ReceiverClientId string `protobuf:"bytes,1,opt,name=receiver_client_id,json=receiverClientId,proto3" json:"receiver_client_id,omitempty"`
	// message content
	Content       string `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendChatMessageRequest) Reset() {
	*x = SendChatMessageRequest{}
	mi := &file_agentassist_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendChatMessageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendChatMessageRequest) ProtoMessage() {}

func (x *SendChatMessageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_agentassist_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendChatMessageRequest.ProtoReflect.Descriptor instead.
func (*SendChatMessageRequest) Descriptor() ([]byte, []int) {
	return file_agentassist_proto_rawDescGZIP(), []int{22}
}

func (x *SendChatMessageRequest) GetReceiverClientId() string {
	if x != nil {
		return x.ReceiverClientId
	}
	return ""
}

func (x *SendChatMessageRequest) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

// ChatMessageNotification represents a notification of a new chat message
type ChatMessageNotification struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// the chat message
	ChatMessage   *ChatMessage `protobuf:"bytes,1,opt,name=chat_message,json=chatMessage,proto3" json:"chat_message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChatMessageNotification) Reset() {
	*x = ChatMessageNotification{}
	mi := &file_agentassist_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChatMessageNotification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatMessageNotification) ProtoMessage() {}

func (x *ChatMessageNotification) ProtoReflect() protoreflect.Message {
	mi := &file_agentassist_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatMessageNotification.ProtoReflect.Descriptor instead.
func (*ChatMessageNotification) Descriptor() ([]byte, []int) {
	return file_agentassist_proto_rawDescGZIP(), []int{23}
}

func (x *ChatMessageNotification) GetChatMessage() *ChatMessage {
	if x != nil {
		return x.ChatMessage
	}
	return nil
}

// SendBackspaceRequest represents a request to send a backspace command
type SendBackspaceRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// receiver client id
	ReceiverClientId string `protobuf:"bytes,1,opt,name=receiver_client_id,json=receiverClientId,proto3" json:"receiver_client_id,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *SendBackspaceRequest) Reset() {
	*x = SendBackspaceRequest{}
	mi := &file_agentassist_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendBackspaceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendBackspaceRequest) ProtoMessage() {}

func (x *SendBackspaceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_agentassist_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendBackspaceRequest.ProtoReflect.Descriptor instead.
func (*SendBackspaceRequest) Descriptor() ([]byte, []int) {
	return file_agentassist_proto_rawDescGZIP(), []int{24}
}

func (x *SendBackspaceRequest) GetReceiverClientId() string {
	if x != nil {
		return x.ReceiverClientId
	}
	return ""
}

// UserLoginResponse represents the response to a user login
type UserLoginResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// client id assigned by server
	ClientId string `protobuf:"bytes,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	// success status
	Success bool `protobuf:"varint,2,opt,name=success,proto3" json:"success,omitempty"`
	// error message if login failed
	ErrorMessage  string `protobuf:"bytes,3,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserLoginResponse) Reset() {
	*x = UserLoginResponse{}
	mi := &file_agentassist_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserLoginResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserLoginResponse) ProtoMessage() {}

func (x *UserLoginResponse) ProtoReflect() protoreflect.Message {
	mi := &file_agentassist_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserLoginResponse.ProtoReflect.Descriptor instead.
func (*UserLoginResponse) Descriptor() ([]byte, []int) {
	return file_agentassist_proto_rawDescGZIP(), []int{25}
}

func (x *UserLoginResponse) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *UserLoginResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *UserLoginResponse) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

// UserConnectionStatusNotification represents a notification when a user connects or disconnects
type UserConnectionStatusNotification struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// the user who connected/disconnected
	User *OnlineUser `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	// connection status: "connected" or "disconnected"
	Status string `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	// timestamp of the status change
	Timestamp     int64 `protobuf:"varint,3,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserConnectionStatusNotification) Reset() {
	*x = UserConnectionStatusNotification{}
	mi := &file_agentassist_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserConnectionStatusNotification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserConnectionStatusNotification) ProtoMessage() {}

func (x *UserConnectionStatusNotification) ProtoReflect() protoreflect.Message {
	mi := &file_agentassist_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserConnectionStatusNotification.ProtoReflect.Descriptor instead.
func (*UserConnectionStatusNotification) Descriptor() ([]byte, []int) {
	return file_agentassist_proto_rawDescGZIP(), []int{26}
}

func (x *UserConnectionStatusNotification) GetUser() *OnlineUser {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *UserConnectionStatusNotification) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *UserConnectionStatusNotification) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

type WebsocketMessage struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// WebsocketMessage cmd
	// AskQuestion: mcp ask_question
	// TaskFinish: mcp task_finish
	// AskQuestionReply: user ask_question reply
	// TaskFinishReply: user task_finish reply
	// UserLogin: user login, str param is user token, nickname is user nickname
	// AskQuestionReplyNotification: notification of an AskQuestionReply
	// TaskFinishReplyNotification: notification of a TaskFinishReply
	// CheckMessageValidity: check if messages are still valid
	// GetPendingMessages: get all pending messages for a user
	// RequestCancelled: notification that a request has been cancelled
	// GetOnlineUsers: get online users with the same token
	// SendChatMessage: send a chat message to another user
	// ChatMessageNotification: notification of a new chat message
	// SendBackspace: send a backspace command to another user
	Cmd string `protobuf:"bytes,1,opt,name=Cmd,proto3" json:"Cmd,omitempty"`
	// ask question
	AskQuestionRequest *AskQuestionRequest `protobuf:"bytes,2,opt,name=AskQuestionRequest,proto3" json:"AskQuestionRequest,omitempty"`
	// task finish
	TaskFinishRequest *TaskFinishRequest `protobuf:"bytes,3,opt,name=TaskFinishRequest,proto3" json:"TaskFinishRequest,omitempty"`
	// ask question reply
	AskQuestionResponse *AskQuestionResponse `protobuf:"bytes,4,opt,name=AskQuestionResponse,proto3" json:"AskQuestionResponse,omitempty"`
	// task finish reply
	TaskFinishResponse *TaskFinishResponse `protobuf:"bytes,5,opt,name=TaskFinishResponse,proto3" json:"TaskFinishResponse,omitempty"`
	// check message validity
	CheckMessageValidityRequest *CheckMessageValidityRequest `protobuf:"bytes,13,opt,name=CheckMessageValidityRequest,proto3" json:"CheckMessageValidityRequest,omitempty"`
	// check message validity response
	CheckMessageValidityResponse *CheckMessageValidityResponse `protobuf:"bytes,14,opt,name=CheckMessageValidityResponse,proto3" json:"CheckMessageValidityResponse,omitempty"`
	// get pending messages request
	GetPendingMessagesRequest *GetPendingMessagesRequest `protobuf:"bytes,15,opt,name=GetPendingMessagesRequest,proto3" json:"GetPendingMessagesRequest,omitempty"`
	// get pending messages response
	GetPendingMessagesResponse *GetPendingMessagesResponse `protobuf:"bytes,16,opt,name=GetPendingMessagesResponse,proto3" json:"GetPendingMessagesResponse,omitempty"`
	// request cancelled notification
	RequestCancelledNotification *RequestCancelledNotification `protobuf:"bytes,17,opt,name=RequestCancelledNotification,proto3" json:"RequestCancelledNotification,omitempty"`
	// get online users request
	GetOnlineUsersRequest *GetOnlineUsersRequest `protobuf:"bytes,19,opt,name=GetOnlineUsersRequest,proto3" json:"GetOnlineUsersRequest,omitempty"`
	// get online users response
	GetOnlineUsersResponse *GetOnlineUsersResponse `protobuf:"bytes,20,opt,name=GetOnlineUsersResponse,proto3" json:"GetOnlineUsersResponse,omitempty"`
	// send chat message request
	SendChatMessageRequest *SendChatMessageRequest `protobuf:"bytes,21,opt,name=SendChatMessageRequest,proto3" json:"SendChatMessageRequest,omitempty"`
	// chat message notification
	ChatMessageNotification *ChatMessageNotification `protobuf:"bytes,22,opt,name=ChatMessageNotification,proto3" json:"ChatMessageNotification,omitempty"`
	// user login response
	UserLoginResponse *UserLoginResponse `protobuf:"bytes,23,opt,name=UserLoginResponse,proto3" json:"UserLoginResponse,omitempty"`
	// user connection status notification
	UserConnectionStatusNotification *UserConnectionStatusNotification `protobuf:"bytes,24,opt,name=UserConnectionStatusNotification,proto3" json:"UserConnectionStatusNotification,omitempty"`
	// send backspace request
	SendBackspaceRequest *SendBackspaceRequest `protobuf:"bytes,25,opt,name=SendBackspaceRequest,proto3" json:"SendBackspaceRequest,omitempty"`
	// str param
	StrParam string `protobuf:"bytes,12,opt,name=StrParam,proto3" json:"StrParam,omitempty"`
	// user nickname (for UserLogin and notifications)
	Nickname      string `protobuf:"bytes,18,opt,name=Nickname,proto3" json:"Nickname,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WebsocketMessage) Reset() {
	*x = WebsocketMessage{}
	mi := &file_agentassist_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WebsocketMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebsocketMessage) ProtoMessage() {}

func (x *WebsocketMessage) ProtoReflect() protoreflect.Message {
	mi := &file_agentassist_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebsocketMessage.ProtoReflect.Descriptor instead.
func (*WebsocketMessage) Descriptor() ([]byte, []int) {
	return file_agentassist_proto_rawDescGZIP(), []int{27}
}

func (x *WebsocketMessage) GetCmd() string {
	if x != nil {
		return x.Cmd
	}
	return ""
}

func (x *WebsocketMessage) GetAskQuestionRequest() *AskQuestionRequest {
	if x != nil {
		return x.AskQuestionRequest
	}
	return nil
}

func (x *WebsocketMessage) GetTaskFinishRequest() *TaskFinishRequest {
	if x != nil {
		return x.TaskFinishRequest
	}
	return nil
}

func (x *WebsocketMessage) GetAskQuestionResponse() *AskQuestionResponse {
	if x != nil {
		return x.AskQuestionResponse
	}
	return nil
}

func (x *WebsocketMessage) GetTaskFinishResponse() *TaskFinishResponse {
	if x != nil {
		return x.TaskFinishResponse
	}
	return nil
}

func (x *WebsocketMessage) GetCheckMessageValidityRequest() *CheckMessageValidityRequest {
	if x != nil {
		return x.CheckMessageValidityRequest
	}
	return nil
}

func (x *WebsocketMessage) GetCheckMessageValidityResponse() *CheckMessageValidityResponse {
	if x != nil {
		return x.CheckMessageValidityResponse
	}
	return nil
}

func (x *WebsocketMessage) GetGetPendingMessagesRequest() *GetPendingMessagesRequest {
	if x != nil {
		return x.GetPendingMessagesRequest
	}
	return nil
}

func (x *WebsocketMessage) GetGetPendingMessagesResponse() *GetPendingMessagesResponse {
	if x != nil {
		return x.GetPendingMessagesResponse
	}
	return nil
}

func (x *WebsocketMessage) GetRequestCancelledNotification() *RequestCancelledNotification {
	if x != nil {
		return x.RequestCancelledNotification
	}
	return nil
}

func (x *WebsocketMessage) GetGetOnlineUsersRequest() *GetOnlineUsersRequest {
	if x != nil {
		return x.GetOnlineUsersRequest
	}
	return nil
}

func (x *WebsocketMessage) GetGetOnlineUsersResponse() *GetOnlineUsersResponse {
	if x != nil {
		return x.GetOnlineUsersResponse
	}
	return nil
}

func (x *WebsocketMessage) GetSendChatMessageRequest() *SendChatMessageRequest {
	if x != nil {
		return x.SendChatMessageRequest
	}
	return nil
}

func (x *WebsocketMessage) GetChatMessageNotification() *ChatMessageNotification {
	if x != nil {
		return x.ChatMessageNotification
	}
	return nil
}

func (x *WebsocketMessage) GetUserLoginResponse() *UserLoginResponse {
	if x != nil {
		return x.UserLoginResponse
	}
	return nil
}

func (x *WebsocketMessage) GetUserConnectionStatusNotification() *UserConnectionStatusNotification {
	if x != nil {
		return x.UserConnectionStatusNotification
	}
	return nil
}

func (x *WebsocketMessage) GetSendBackspaceRequest() *SendBackspaceRequest {
	if x != nil {
		return x.SendBackspaceRequest
	}
	return nil
}

func (x *WebsocketMessage) GetStrParam() string {
	if x != nil {
		return x.StrParam
	}
	return ""
}

func (x *WebsocketMessage) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

var File_agentassist_proto protoreflect.FileDescriptor

const file_agentassist_proto_rawDesc = "" +
	"\n" +
	"\x11agentassist.proto\x12\x10agentassistproto\"5\n" +
	"\vTextContent\x12\x12\n" +
	"\x04type\x18\x01 \x01(\tR\x04type\x12\x12\n" +
	"\x04text\x18\x02 \x01(\tR\x04text\"S\n" +
	"\fImageContent\x12\x12\n" +
	"\x04type\x18\x01 \x01(\tR\x04type\x12\x12\n" +
	"\x04data\x18\x02 \x01(\tR\x04data\x12\x1b\n" +
	"\tmime_type\x18\x03 \x01(\tR\bmimeType\"S\n" +
	"\fAudioContent\x12\x12\n" +
	"\x04type\x18\x01 \x01(\tR\x04type\x12\x12\n" +
	"\x04data\x18\x02 \x01(\tR\x04data\x12\x1b\n" +
	"\tmime_type\x18\x03 \x01(\tR\bmimeType\"i\n" +
	"\x10EmbeddedResource\x12\x12\n" +
	"\x04type\x18\x01 \x01(\tR\x04type\x12\x10\n" +
	"\x03uri\x18\x02 \x01(\tR\x03uri\x12\x1b\n" +
	"\tmime_type\x18\x03 \x01(\tR\bmimeType\x12\x12\n" +
	"\x04data\x18\x04 \x01(\fR\x04data\"\x96\x02\n" +
	"\x10McpResultContent\x12\x12\n" +
	"\x04type\x18\x01 \x01(\x05R\x04type\x121\n" +
	"\x04text\x18\x02 \x01(\v2\x1d.agentassistproto.TextContentR\x04text\x124\n" +
	"\x05image\x18\x03 \x01(\v2\x1e.agentassistproto.ImageContentR\x05image\x124\n" +
	"\x05audio\x18\x04 \x01(\v2\x1e.agentassistproto.AudioContentR\x05audio\x12O\n" +
	"\x11embedded_resource\x18\x05 \x01(\v2\".agentassistproto.EmbeddedResourceR\x10embeddedResource\"\n" +
	"\n" +
	"\bMsgEmpty\"y\n" +
	"\x15McpAskQuestionRequest\x12*\n" +
	"\x10ProjectDirectory\x18\x01 \x01(\tR\x10ProjectDirectory\x12\x1a\n" +
	"\bQuestion\x18\x02 \x01(\tR\bQuestion\x12\x18\n" +
	"\aTimeout\x18\x03 \x01(\x05R\aTimeout\"\x85\x01\n" +
	"\x12AskQuestionRequest\x12\x0e\n" +
	"\x02ID\x18\x01 \x01(\tR\x02ID\x12\x1c\n" +
	"\tUserToken\x18\x02 \x01(\tR\tUserToken\x12A\n" +
	"\aRequest\x18\x03 \x01(\v2'.agentassistproto.McpAskQuestionRequestR\aRequest\"\xfd\x01\n" +
	"\x13AskQuestionResponse\x12\x0e\n" +
	"\x02ID\x18\x01 \x01(\tR\x02ID\x12\x18\n" +
	"\aIsError\x18\x02 \x01(\bR\aIsError\x12C\n" +
	"\x04Meta\x18\x03 \x03(\v2/.agentassistproto.AskQuestionResponse.MetaEntryR\x04Meta\x12>\n" +
	"\bcontents\x18\x04 \x03(\v2\".agentassistproto.McpResultContentR\bcontents\x1a7\n" +
	"\tMetaEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"v\n" +
	"\x14McpTaskFinishRequest\x12*\n" +
	"\x10ProjectDirectory\x18\x01 \x01(\tR\x10ProjectDirectory\x12\x18\n" +
	"\aSummary\x18\x02 \x01(\tR\aSummary\x12\x18\n" +
	"\aTimeout\x18\x03 \x01(\x05R\aTimeout\"\x83\x01\n" +
	"\x11TaskFinishRequest\x12\x0e\n" +
	"\x02ID\x18\x01 \x01(\tR\x02ID\x12\x1c\n" +
	"\tUserToken\x18\x02 \x01(\tR\tUserToken\x12@\n" +
	"\aRequest\x18\x03 \x01(\v2&.agentassistproto.McpTaskFinishRequestR\aRequest\"\xfb\x01\n" +
	"\x12TaskFinishResponse\x12\x0e\n" +
	"\x02ID\x18\x01 \x01(\tR\x02ID\x12\x18\n" +
	"\aIsError\x18\x02 \x01(\bR\aIsError\x12B\n" +
	"\x04Meta\x18\x03 \x03(\v2..agentassistproto.TaskFinishResponse.MetaEntryR\x04Meta\x12>\n" +
	"\bcontents\x18\x04 \x03(\v2\".agentassistproto.McpResultContentR\bcontents\x1a7\n" +
	"\tMetaEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\">\n" +
	"\x1bCheckMessageValidityRequest\x12\x1f\n" +
	"\vrequest_ids\x18\x01 \x03(\tR\n" +
	"requestIds\"\xb5\x01\n" +
	"\x1cCheckMessageValidityResponse\x12X\n" +
	"\bvalidity\x18\x01 \x03(\v2<.agentassistproto.CheckMessageValidityResponse.ValidityEntryR\bvalidity\x1a;\n" +
	"\rValidityEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\bR\x05value:\x028\x01\":\n" +
	"\x19GetPendingMessagesRequest\x12\x1d\n" +
	"\n" +
	"user_token\x18\x01 \x01(\tR\tuserToken\"\x99\x02\n" +
	"\x0ePendingMessage\x12!\n" +
	"\fmessage_type\x18\x01 \x01(\tR\vmessageType\x12V\n" +
	"\x14ask_question_request\x18\x02 \x01(\v2$.agentassistproto.AskQuestionRequestR\x12askQuestionRequest\x12S\n" +
	"\x13task_finish_request\x18\x03 \x01(\v2#.agentassistproto.TaskFinishRequestR\x11taskFinishRequest\x12\x1d\n" +
	"\n" +
	"created_at\x18\x04 \x01(\x03R\tcreatedAt\x12\x18\n" +
	"\atimeout\x18\x05 \x01(\x05R\atimeout\"\x8a\x01\n" +
	"\x1aGetPendingMessagesResponse\x12K\n" +
	"\x10pending_messages\x18\x01 \x03(\v2 .agentassistproto.PendingMessageR\x0fpendingMessages\x12\x1f\n" +
	"\vtotal_count\x18\x02 \x01(\x05R\n" +
	"totalCount\"x\n" +
	"\x1cRequestCancelledNotification\x12\x1d\n" +
	"\n" +
	"request_id\x18\x01 \x01(\tR\trequestId\x12\x16\n" +
	"\x06reason\x18\x02 \x01(\tR\x06reason\x12!\n" +
	"\fmessage_type\x18\x03 \x01(\tR\vmessageType\"h\n" +
	"\n" +
	"OnlineUser\x12\x1b\n" +
	"\tclient_id\x18\x01 \x01(\tR\bclientId\x12\x1a\n" +
	"\bnickname\x18\x02 \x01(\tR\bnickname\x12!\n" +
	"\fconnected_at\x18\x03 \x01(\x03R\vconnectedAt\"6\n" +
	"\x15GetOnlineUsersRequest\x12\x1d\n" +
	"\n" +
	"user_token\x18\x01 \x01(\tR\tuserToken\"z\n" +
	"\x16GetOnlineUsersResponse\x12?\n" +
	"\fonline_users\x18\x01 \x03(\v2\x1c.agentassistproto.OnlineUserR\vonlineUsers\x12\x1f\n" +
	"\vtotal_count\x18\x02 \x01(\x05R\n" +
	"totalCount\"\x8d\x02\n" +
	"\vChatMessage\x12\x1d\n" +
	"\n" +
	"message_id\x18\x01 \x01(\tR\tmessageId\x12(\n" +
	"\x10sender_client_id\x18\x02 \x01(\tR\x0esenderClientId\x12'\n" +
	"\x0fsender_nickname\x18\x03 \x01(\tR\x0esenderNickname\x12,\n" +
	"\x12receiver_client_id\x18\x04 \x01(\tR\x10receiverClientId\x12+\n" +
	"\x11receiver_nickname\x18\x05 \x01(\tR\x10receiverNickname\x12\x18\n" +
	"\acontent\x18\x06 \x01(\tR\acontent\x12\x17\n" +
	"\asent_at\x18\a \x01(\x03R\x06sentAt\"`\n" +
	"\x16SendChatMessageRequest\x12,\n" +
	"\x12receiver_client_id\x18\x01 \x01(\tR\x10receiverClientId\x12\x18\n" +
	"\acontent\x18\x02 \x01(\tR\acontent\"[\n" +
	"\x17ChatMessageNotification\x12@\n" +
	"\fchat_message\x18\x01 \x01(\v2\x1d.agentassistproto.ChatMessageR\vchatMessage\"D\n" +
	"\x14SendBackspaceRequest\x12,\n" +
	"\x12receiver_client_id\x18\x01 \x01(\tR\x10receiverClientId\"o\n" +
	"\x11UserLoginResponse\x12\x1b\n" +
	"\tclient_id\x18\x01 \x01(\tR\bclientId\x12\x18\n" +
	"\asuccess\x18\x02 \x01(\bR\asuccess\x12#\n" +
	"\rerror_message\x18\x03 \x01(\tR\ferrorMessage\"\x8a\x01\n" +
	" UserConnectionStatusNotification\x120\n" +
	"\x04user\x18\x01 \x01(\v2\x1c.agentassistproto.OnlineUserR\x04user\x12\x16\n" +
	"\x06status\x18\x02 \x01(\tR\x06status\x12\x1c\n" +
	"\ttimestamp\x18\x03 \x01(\x03R\ttimestamp\"\x9d\r\n" +
	"\x10WebsocketMessage\x12\x10\n" +
	"\x03Cmd\x18\x01 \x01(\tR\x03Cmd\x12T\n" +
	"\x12AskQuestionRequest\x18\x02 \x01(\v2$.agentassistproto.AskQuestionRequestR\x12AskQuestionRequest\x12Q\n" +
	"\x11TaskFinishRequest\x18\x03 \x01(\v2#.agentassistproto.TaskFinishRequestR\x11TaskFinishRequest\x12W\n" +
	"\x13AskQuestionResponse\x18\x04 \x01(\v2%.agentassistproto.AskQuestionResponseR\x13AskQuestionResponse\x12T\n" +
	"\x12TaskFinishResponse\x18\x05 \x01(\v2$.agentassistproto.TaskFinishResponseR\x12TaskFinishResponse\x12o\n" +
	"\x1bCheckMessageValidityRequest\x18\r \x01(\v2-.agentassistproto.CheckMessageValidityRequestR\x1bCheckMessageValidityRequest\x12r\n" +
	"\x1cCheckMessageValidityResponse\x18\x0e \x01(\v2..agentassistproto.CheckMessageValidityResponseR\x1cCheckMessageValidityResponse\x12i\n" +
	"\x19GetPendingMessagesRequest\x18\x0f \x01(\v2+.agentassistproto.GetPendingMessagesRequestR\x19GetPendingMessagesRequest\x12l\n" +
	"\x1aGetPendingMessagesResponse\x18\x10 \x01(\v2,.agentassistproto.GetPendingMessagesResponseR\x1aGetPendingMessagesResponse\x12r\n" +
	"\x1cRequestCancelledNotification\x18\x11 \x01(\v2..agentassistproto.RequestCancelledNotificationR\x1cRequestCancelledNotification\x12]\n" +
	"\x15GetOnlineUsersRequest\x18\x13 \x01(\v2'.agentassistproto.GetOnlineUsersRequestR\x15GetOnlineUsersRequest\x12`\n" +
	"\x16GetOnlineUsersResponse\x18\x14 \x01(\v2(.agentassistproto.GetOnlineUsersResponseR\x16GetOnlineUsersResponse\x12`\n" +
	"\x16SendChatMessageRequest\x18\x15 \x01(\v2(.agentassistproto.SendChatMessageRequestR\x16SendChatMessageRequest\x12c\n" +
	"\x17ChatMessageNotification\x18\x16 \x01(\v2).agentassistproto.ChatMessageNotificationR\x17ChatMessageNotification\x12Q\n" +
	"\x11UserLoginResponse\x18\x17 \x01(\v2#.agentassistproto.UserLoginResponseR\x11UserLoginResponse\x12~\n" +
	" UserConnectionStatusNotification\x18\x18 \x01(\v22.agentassistproto.UserConnectionStatusNotificationR UserConnectionStatusNotification\x12Z\n" +
	"\x14SendBackspaceRequest\x18\x19 \x01(\v2&.agentassistproto.SendBackspaceRequestR\x14SendBackspaceRequest\x12\x1a\n" +
	"\bStrParam\x18\f \x01(\tR\bStrParam\x12\x1a\n" +
	"\bNickname\x18\x12 \x01(\tR\bNickname2\xc5\x01\n" +
	"\x0eSrvAgentAssist\x12Z\n" +
	"\vAskQuestion\x12$.agentassistproto.AskQuestionRequest\x1a%.agentassistproto.AskQuestionResponse\x12W\n" +
	"\n" +
	"TaskFinish\x12#.agentassistproto.TaskFinishRequest\x1a$.agentassistproto.TaskFinishResponseB8Z6github.com/yangjuncode/agentassistant/agentassistprotob\x06proto3"

var (
	file_agentassist_proto_rawDescOnce sync.Once
	file_agentassist_proto_rawDescData []byte
)

func file_agentassist_proto_rawDescGZIP() []byte {
	file_agentassist_proto_rawDescOnce.Do(func() {
		file_agentassist_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_agentassist_proto_rawDesc), len(file_agentassist_proto_rawDesc)))
	})
	return file_agentassist_proto_rawDescData
}

var file_agentassist_proto_msgTypes = make([]protoimpl.MessageInfo, 31)
var file_agentassist_proto_goTypes = []any{
	(*TextContent)(nil),                      // 0: agentassistproto.TextContent
	(*ImageContent)(nil),                     // 1: agentassistproto.ImageContent
	(*AudioContent)(nil),                     // 2: agentassistproto.AudioContent
	(*EmbeddedResource)(nil),                 // 3: agentassistproto.EmbeddedResource
	(*McpResultContent)(nil),                 // 4: agentassistproto.McpResultContent
	(*MsgEmpty)(nil),                         // 5: agentassistproto.MsgEmpty
	(*McpAskQuestionRequest)(nil),            // 6: agentassistproto.McpAskQuestionRequest
	(*AskQuestionRequest)(nil),               // 7: agentassistproto.AskQuestionRequest
	(*AskQuestionResponse)(nil),              // 8: agentassistproto.AskQuestionResponse
	(*McpTaskFinishRequest)(nil),             // 9: agentassistproto.McpTaskFinishRequest
	(*TaskFinishRequest)(nil),                // 10: agentassistproto.TaskFinishRequest
	(*TaskFinishResponse)(nil),               // 11: agentassistproto.TaskFinishResponse
	(*CheckMessageValidityRequest)(nil),      // 12: agentassistproto.CheckMessageValidityRequest
	(*CheckMessageValidityResponse)(nil),     // 13: agentassistproto.CheckMessageValidityResponse
	(*GetPendingMessagesRequest)(nil),        // 14: agentassistproto.GetPendingMessagesRequest
	(*PendingMessage)(nil),                   // 15: agentassistproto.PendingMessage
	(*GetPendingMessagesResponse)(nil),       // 16: agentassistproto.GetPendingMessagesResponse
	(*RequestCancelledNotification)(nil),     // 17: agentassistproto.RequestCancelledNotification
	(*OnlineUser)(nil),                       // 18: agentassistproto.OnlineUser
	(*GetOnlineUsersRequest)(nil),            // 19: agentassistproto.GetOnlineUsersRequest
	(*GetOnlineUsersResponse)(nil),           // 20: agentassistproto.GetOnlineUsersResponse
	(*ChatMessage)(nil),                      // 21: agentassistproto.ChatMessage
	(*SendChatMessageRequest)(nil),           // 22: agentassistproto.SendChatMessageRequest
	(*ChatMessageNotification)(nil),          // 23: agentassistproto.ChatMessageNotification
	(*SendBackspaceRequest)(nil),             // 24: agentassistproto.SendBackspaceRequest
	(*UserLoginResponse)(nil),                // 25: agentassistproto.UserLoginResponse
	(*UserConnectionStatusNotification)(nil), // 26: agentassistproto.UserConnectionStatusNotification
	(*WebsocketMessage)(nil),                 // 27: agentassistproto.WebsocketMessage
	nil,                                      // 28: agentassistproto.AskQuestionResponse.MetaEntry
	nil,                                      // 29: agentassistproto.TaskFinishResponse.MetaEntry
	nil,                                      // 30: agentassistproto.CheckMessageValidityResponse.ValidityEntry
}
var file_agentassist_proto_depIdxs = []int32{
	0,  // 0: agentassistproto.McpResultContent.text:type_name -> agentassistproto.TextContent
	1,  // 1: agentassistproto.McpResultContent.image:type_name -> agentassistproto.ImageContent
	2,  // 2: agentassistproto.McpResultContent.audio:type_name -> agentassistproto.AudioContent
	3,  // 3: agentassistproto.McpResultContent.embedded_resource:type_name -> agentassistproto.EmbeddedResource
	6,  // 4: agentassistproto.AskQuestionRequest.Request:type_name -> agentassistproto.McpAskQuestionRequest
	28, // 5: agentassistproto.AskQuestionResponse.Meta:type_name -> agentassistproto.AskQuestionResponse.MetaEntry
	4,  // 6: agentassistproto.AskQuestionResponse.contents:type_name -> agentassistproto.McpResultContent
	9,  // 7: agentassistproto.TaskFinishRequest.Request:type_name -> agentassistproto.McpTaskFinishRequest
	29, // 8: agentassistproto.TaskFinishResponse.Meta:type_name -> agentassistproto.TaskFinishResponse.MetaEntry
	4,  // 9: agentassistproto.TaskFinishResponse.contents:type_name -> agentassistproto.McpResultContent
	30, // 10: agentassistproto.CheckMessageValidityResponse.validity:type_name -> agentassistproto.CheckMessageValidityResponse.ValidityEntry
	7,  // 11: agentassistproto.PendingMessage.ask_question_request:type_name -> agentassistproto.AskQuestionRequest
	10, // 12: agentassistproto.PendingMessage.task_finish_request:type_name -> agentassistproto.TaskFinishRequest
	15, // 13: agentassistproto.GetPendingMessagesResponse.pending_messages:type_name -> agentassistproto.PendingMessage
	18, // 14: agentassistproto.GetOnlineUsersResponse.online_users:type_name -> agentassistproto.OnlineUser
	21, // 15: agentassistproto.ChatMessageNotification.chat_message:type_name -> agentassistproto.ChatMessage
	18, // 16: agentassistproto.UserConnectionStatusNotification.user:type_name -> agentassistproto.OnlineUser
	7,  // 17: agentassistproto.WebsocketMessage.AskQuestionRequest:type_name -> agentassistproto.AskQuestionRequest
	10, // 18: agentassistproto.WebsocketMessage.TaskFinishRequest:type_name -> agentassistproto.TaskFinishRequest
	8,  // 19: agentassistproto.WebsocketMessage.AskQuestionResponse:type_name -> agentassistproto.AskQuestionResponse
	11, // 20: agentassistproto.WebsocketMessage.TaskFinishResponse:type_name -> agentassistproto.TaskFinishResponse
	12, // 21: agentassistproto.WebsocketMessage.CheckMessageValidityRequest:type_name -> agentassistproto.CheckMessageValidityRequest
	13, // 22: agentassistproto.WebsocketMessage.CheckMessageValidityResponse:type_name -> agentassistproto.CheckMessageValidityResponse
	14, // 23: agentassistproto.WebsocketMessage.GetPendingMessagesRequest:type_name -> agentassistproto.GetPendingMessagesRequest
	16, // 24: agentassistproto.WebsocketMessage.GetPendingMessagesResponse:type_name -> agentassistproto.GetPendingMessagesResponse
	17, // 25: agentassistproto.WebsocketMessage.RequestCancelledNotification:type_name -> agentassistproto.RequestCancelledNotification
	19, // 26: agentassistproto.WebsocketMessage.GetOnlineUsersRequest:type_name -> agentassistproto.GetOnlineUsersRequest
	20, // 27: agentassistproto.WebsocketMessage.GetOnlineUsersResponse:type_name -> agentassistproto.GetOnlineUsersResponse
	22, // 28: agentassistproto.WebsocketMessage.SendChatMessageRequest:type_name -> agentassistproto.SendChatMessageRequest
	23, // 29: agentassistproto.WebsocketMessage.ChatMessageNotification:type_name -> agentassistproto.ChatMessageNotification
	25, // 30: agentassistproto.WebsocketMessage.UserLoginResponse:type_name -> agentassistproto.UserLoginResponse
	26, // 31: agentassistproto.WebsocketMessage.UserConnectionStatusNotification:type_name -> agentassistproto.UserConnectionStatusNotification
	24, // 32: agentassistproto.WebsocketMessage.SendBackspaceRequest:type_name -> agentassistproto.SendBackspaceRequest
	7,  // 33: agentassistproto.SrvAgentAssist.AskQuestion:input_type -> agentassistproto.AskQuestionRequest
	10, // 34: agentassistproto.SrvAgentAssist.TaskFinish:input_type -> agentassistproto.TaskFinishRequest
	8,  // 35: agentassistproto.SrvAgentAssist.AskQuestion:output_type -> agentassistproto.AskQuestionResponse
	11, // 36: agentassistproto.SrvAgentAssist.TaskFinish:output_type -> agentassistproto.TaskFinishResponse
	35, // [35:37] is the sub-list for method output_type
	33, // [33:35] is the sub-list for method input_type
	33, // [33:33] is the sub-list for extension type_name
	33, // [33:33] is the sub-list for extension extendee
	0,  // [0:33] is the sub-list for field type_name
}

func init() { file_agentassist_proto_init() }
func file_agentassist_proto_init() {
	if File_agentassist_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_agentassist_proto_rawDesc), len(file_agentassist_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   31,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_agentassist_proto_goTypes,
		DependencyIndexes: file_agentassist_proto_depIdxs,
		MessageInfos:      file_agentassist_proto_msgTypes,
	}.Build()
	File_agentassist_proto = out.File
	file_agentassist_proto_goTypes = nil
	file_agentassist_proto_depIdxs = nil
}
